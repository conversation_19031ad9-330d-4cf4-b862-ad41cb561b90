"use client";

import { Pie } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, Legend } from "chart.js";
import { <PERSON>, <PERSON><PERSON><PERSON>, CardHeader, Divider, Spinner } from "@heroui/react";
import { db } from "@/firebase";
import { collection, getDocs } from "firebase/firestore";
import { useEffect, useState } from "react";
import { getAuth } from "firebase/auth";

// Register Chart.js elements
ChartJS.register(ArcElement, Tooltip, Legend);

export default function LacionChart() {
  const [chartData, setChartData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchChartData = async () => {
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) return;

      const casesRef = collection(
        db,
        "healthradarDB",
        "users",
        "healthworker",
        user.uid,
        "UploadedCases"
      );

      const snapshot = await getDocs(casesRef);
      const data = snapshot.docs.map((doc) => doc.data());

      // 🧠 Filter for Municipality = "Lilo-an"
      const filteredData = data.filter(
        (item) => item.Municipality?.toLowerCase() === "consolacion"
      );

      console.log("Raw data:", data);
      console.log("Filtered data:", filteredData);

      // 📊 Aggregate case counts by disease within "Lilo-an"
      const diseaseMap: Record<string, number> = {};
      filteredData.forEach((item) => {
        const name = item.DiseaseName;
        const count = parseInt(item.CaseCount, 10);
        if (!isNaN(count)) {
          diseaseMap[name] = (diseaseMap[name] || 0) + count;
        }
      });

      const labels = Object.keys(diseaseMap);
      const values = Object.values(diseaseMap);
      const disease = labels.map(() => `hsl(${Math.random() * 360}, 70%, 60%)`);

      setChartData({
        labels,
        datasets: [
          {
            label: "Disease Case Count",
            data: values,
            backgroundColor: disease,
            hoverOffset: 4,
          },
        ],
      });

      setLoading(false);
    };

    fetchChartData();
  }, []);

  return (
    <div>
      <Card className="max-w-[400px]">
        <CardHeader className="flex gap-3 items-center text-black">
          Consolacion Chart
        </CardHeader>
        <Divider />
        <CardBody>
          {loading ? <Spinner size="lg" /> : <Pie data={chartData} />}
        </CardBody>
      </Card>
    </div>
  );
}
