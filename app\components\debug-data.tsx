"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardBody, CardHeader, Divider } from "@heroui/react";
import { db } from "@/firebase";
import { collection, getDocs } from "firebase/firestore";
import { getAuth } from "firebase/auth";

export default function DebugData() {
  const [rawData, setRawData] = useState<any[]>([]);
  const [municipalities, setMunicipalities] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) {
        console.log("No user authenticated");
        setLoading(false);
        return;
      }

      try {
        const casesRef = collection(
          db,
          "healthradarDB",
          "users",
          "healthworker",
          user.uid,
          "UploadedCases"
        );

        const snapshot = await getDocs(casesRef);
        const data = snapshot.docs.map((doc) => doc.data());
        
        setRawData(data);

        // Extract unique municipalities
        const uniqueMunicipalities = [
          ...new Set(
            data
              .map((item) => item.Municipality)
              .filter((muni) => muni)
              .map((muni) => `"${muni}"`) // Add quotes to see exact format
          ),
        ];
        
        setMunicipalities(uniqueMunicipalities);
        
        console.log("All raw data:", data);
        console.log("Unique municipalities found:", uniqueMunicipalities);
        
        // Log first few records to see structure
        console.log("Sample records:", data.slice(0, 3));
        
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return <div>Loading debug data...</div>;
  }

  return (
    <div className="p-4">
      <Card className="mb-4">
        <CardHeader>
          <h3 className="text-lg font-bold">Database Debug Information</h3>
        </CardHeader>
        <Divider />
        <CardBody>
          <div className="space-y-4">
            <div>
              <strong>Total Records:</strong> {rawData.length}
            </div>
            
            <div>
              <strong>Unique Municipalities Found:</strong>
              <ul className="list-disc list-inside mt-2">
                {municipalities.map((muni, index) => (
                  <li key={index} className="font-mono text-sm">
                    {muni}
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <strong>Sample Data Structure:</strong>
              <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(rawData.slice(0, 2), null, 2)}
              </pre>
            </div>

            <div>
              <strong>Field Names in First Record:</strong>
              {rawData.length > 0 && (
                <ul className="list-disc list-inside mt-2">
                  {Object.keys(rawData[0]).map((key) => (
                    <li key={key} className="font-mono text-sm">
                      {key}: {typeof rawData[0][key]}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
