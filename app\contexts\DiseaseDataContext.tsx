"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getAuth } from 'firebase/auth';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/firebase';

// Disease color mapping
export const DISEASE_COLORS = {
  tuberculosis: '#8B5CF6',     // Purple
  measles: '#EF4444',          // Red
  malaria: '#F59E0B',          // Amber
  leptospirosis: '#10B981',    // Emerald
  'hiv/aids': '#EC4899',       // Pink
  dengue: '#3B82F6',           // Blue
  cholera: '#06B6D4',          // Cyan
  'syndrome (amses)': '#84CC16', // Lime
  encephalities: '#F97316',    // Orange
  'acute menigitis': '#6366F1', // Indigo
  covid: '#DC2626'             // Red-600
};

export interface DiseaseData {
  Municipality: string;
  DiseaseName: string;
  CaseCount: string;
  Date?: string;
}

export interface ProcessedDiseaseData {
  [diseaseName: string]: {
    totalCases: number;
    municipalities: { [municipality: string]: number };
    color: string;
  };
}

interface DiseaseDataContextType {
  rawData: DiseaseData[];
  processedData: ProcessedDiseaseData;
  loading: boolean;
  refreshData: () => Promise<void>;
}

const DiseaseDataContext = createContext<DiseaseDataContextType | undefined>(undefined);

export const useDiseaseData = () => {
  const context = useContext(DiseaseDataContext);
  if (!context) {
    throw new Error('useDiseaseData must be used within a DiseaseDataProvider');
  }
  return context;
};

interface DiseaseDataProviderProps {
  children: ReactNode;
}

export const DiseaseDataProvider: React.FC<DiseaseDataProviderProps> = ({ children }) => {
  const [rawData, setRawData] = useState<DiseaseData[]>([]);
  const [processedData, setProcessedData] = useState<ProcessedDiseaseData>({});
  const [loading, setLoading] = useState(true);

  const processRawData = (data: DiseaseData[]): ProcessedDiseaseData => {
    const processed: ProcessedDiseaseData = {};

    data.forEach((item) => {
      const diseaseName = item.DiseaseName?.toLowerCase().trim();
      const municipality = item.Municipality?.trim();
      const caseCount = parseInt(item.CaseCount, 10);

      if (diseaseName && municipality && !isNaN(caseCount) && caseCount > 0) {
        if (!processed[diseaseName]) {
          processed[diseaseName] = {
            totalCases: 0,
            municipalities: {},
            color: DISEASE_COLORS[diseaseName as keyof typeof DISEASE_COLORS] || '#6B7280'
          };
        }

        processed[diseaseName].totalCases += caseCount;
        processed[diseaseName].municipalities[municipality] = 
          (processed[diseaseName].municipalities[municipality] || 0) + caseCount;
      }
    });

    return processed;
  };

  const fetchData = async () => {
    setLoading(true);
    try {
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) {
        console.log('No user authenticated');
        setRawData([]);
        setProcessedData({});
        return;
      }

      const casesRef = collection(
        db,
        'healthradarDB',
        'users',
        'healthworker',
        user.uid,
        'UploadedCases'
      );

      const snapshot = await getDocs(casesRef);
      const data = snapshot.docs.map((doc) => doc.data() as DiseaseData);
      
      console.log('Fetched disease data:', data);
      
      setRawData(data);
      setProcessedData(processRawData(data));
    } catch (error) {
      console.error('Error fetching disease data:', error);
      setRawData([]);
      setProcessedData({});
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const value: DiseaseDataContextType = {
    rawData,
    processedData,
    loading,
    refreshData: fetchData
  };

  return (
    <DiseaseDataContext.Provider value={value}>
      {children}
    </DiseaseDataContext.Provider>
  );
};
