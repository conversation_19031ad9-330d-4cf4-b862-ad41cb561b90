"use client";

import { Pie } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Tooltip, Legend, ChartData } from "chart.js";
import { <PERSON>, CardB<PERSON>, CardHeader, Divider, Spinner } from "@heroui/react";
import { db } from "@/firebase";
import { collection, getDocs } from "firebase/firestore";
import { useEffect, useState } from "react";
import { getAuth } from "firebase/auth";

// Register Chart.js elements
ChartJS.register(ArcElement, Tooltip, Legend);

export default function LacionChart() {
  const [chartData, setChartData] = useState<ChartData<'pie'> | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchChartData = async () => {
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) {
        console.log("No user authenticated for Consolacion chart");
        setLoading(false);
        return;
      }

      try {
        const casesRef = collection(
          db,
          "healthradarDB",
          "users",
          "healthworker",
          user.uid,
          "UploadedCases"
        );

        const snapshot = await getDocs(casesRef);
        const data = snapshot.docs.map((doc) => doc.data());

        console.log("Raw data for Consolacion:", data);

        // 🧠 Filter for Municipality = "Consolacion"
        const filteredData = data.filter((item) => {
          const municipality = item.Municipality?.toLowerCase().trim();
          return municipality === "consolacion";
        });

        console.log("Filtered Consolacion data:", filteredData);

        // 📊 Aggregate case counts by disease within "Consolacion"
        const diseaseMap: Record<string, number> = {};
        filteredData.forEach((item) => {
          const name = item.DiseaseName?.trim();
          const count = parseInt(item.CaseCount, 10);
          if (name && !isNaN(count) && count > 0) {
            diseaseMap[name] = (diseaseMap[name] || 0) + count;
          }
        });

        console.log("Disease map for Consolacion:", diseaseMap);

        const labels = Object.keys(diseaseMap);
        const values = Object.values(diseaseMap);

        if (labels.length > 0) {
          const colors = labels.map(() => `hsl(${Math.random() * 360}, 70%, 60%)`);

          setChartData({
            labels,
            datasets: [
              {
                label: "Disease Case Count",
                data: values,
                backgroundColor: colors,
                hoverOffset: 4,
              },
            ],
          });
        } else {
          // Get available municipalities for helpful message
          const availableMunicipalities = [...new Set(data.map(item => item.Municipality).filter(Boolean))];
          console.log("No data found for Consolacion municipality");
          console.log("Available municipalities:", availableMunicipalities);
          setChartData(null);
        }
      } catch (error) {
        console.error("Error fetching Consolacion chart data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchChartData();
  }, []);

  return (
    <div>
      <Card className="max-w-[400px]">
        <CardHeader className="flex gap-3 items-center text-black">
          Consolacion Chart
        </CardHeader>
        <Divider />
        <CardBody>
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <Spinner size="lg" />
            </div>
          ) : chartData ? (
            <div className="h-64">
              <Pie data={chartData} options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'bottom' as const,
                    labels: {
                      padding: 20,
                      usePointStyle: true,
                      font: {
                        size: 12
                      }
                    }
                  }
                }
              }} />
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <p className="text-sm font-semibold text-gray-700 mb-1">No data available for Consolacion</p>
                <p className="text-xs text-gray-500">Upload CSV data to see the chart</p>
              </div>
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
}
