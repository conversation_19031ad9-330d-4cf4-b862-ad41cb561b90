import Body from "./sections/body";
import { useRouter } from "next/navigation";
// import PredictionChart from "../components/prediction-chart";
// import SideNavbar from "../components/side-navbar";

export default function Page() {
  // const router = useRouter();
  // useEffect(() => {
  //   const token = sessionStorage.getItem("token");
  //   //  const [isAuthorized, setIsAuthorized] = useState(false);

  //   if (!token) {
  //     router.push("/components/login");
  //     console.log("No token found, redirecting to login page.");
  //   } else {
  //     // setIsAuthorized(true);
  //     console.log("Token found:", token);
  //   }
  // });
  return (
    <div className="flex flex-col w-full overflow-auto">
      <Body />
    </div>
  );
}
