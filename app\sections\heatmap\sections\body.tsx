"use client";

import { useEffect, useState } from "react";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import { getAuth } from "firebase/auth";
import { collection, getDocs } from "firebase/firestore";
import { db } from "@/firebase";

export default function Body() {
  const [municipalityData, setMunicipalityData] = useState<
    Record<string, number>
  >({});

  // Fetch and process Firestore case data
  useEffect(() => {
    const fetchCaseCounts = async () => {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) return;

      const casesRef = collection(
        db,
        "healthradarDB",
        "users",
        "healthworker",
        user.uid,
        "UploadedCases"
      );

      const snapshot = await getDocs(casesRef);
      const data = snapshot.docs.map((doc) => doc.data());

      const municipalityMap: Record<string, number> = {};
      data.forEach((item) => {
        const muni = item.Municipality;
        const count = parseInt(item.CaseCount, 10);
        if (muni && !isNaN(count)) {
          const key = muni.toLowerCase();
          municipalityMap[key] = (municipalityMap[key] || 0) + count;
        }
      });

      setMunicipalityData(municipalityMap);
    };

    fetchCaseCounts();
  }, []);

  // Load and render map
  useEffect(() => {
    if (Object.keys(municipalityData).length === 0) return;

    const map = L.map("map").setView([10.35, 123.93], 11);

    L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution:
        '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    }).addTo(map);

    const getColor = (count: number) =>
      count > 70
        ? "#800026"
        : count > 50
        ? "#BD0026"
        : count > 30
        ? "#E31A1C"
        : count > 10
        ? "#FC4E2A"
        : "#FED976";

    const featuredMunicipalities: GeoJSON.FeatureCollection = {
      type: "FeatureCollection",
      features: [
        {
          type: "Feature",
          properties: {
            name: "Mandaue City",
            caseCount: municipalityData["mandaue city"] || 0,
          },
          geometry: {
            type: "Polygon",
            coordinates: [
              [
                [123.91, 10.31], // Southwest corner
                [123.96, 10.31], // Southeast corner
                [123.96, 10.35], // Northeast corner
                [123.91, 10.35], // Northwest corner
                [123.91, 10.31], // Close the polygon
              ],
            ],
          },
        },
        {
          type: "Feature",
          properties: {
            name: "Lilo-an",
            caseCount: municipalityData["lilo-an"] || 0,
          },
          geometry: {
            type: "Polygon",
            coordinates: [
              [
                [123.978, 10.396],
                [123.988, 10.396],
                [123.988, 10.408],
                [123.978, 10.408],
                [123.978, 10.396],
              ],
            ],
          },
        },
        {
          type: "Feature",
          properties: {
            name: "Consolacion",
            caseCount: municipalityData["consolacion"] || 0,
          },
          geometry: {
            type: "Polygon",
            coordinates: [
              [
                [123.935, 10.387],
                [123.945, 10.387],
                [123.945, 10.4],
                [123.935, 10.4],
                [123.935, 10.387],
              ],
            ],
          },
        },
      ],
    };

    L.geoJSON(featuredMunicipalities, {
      style: (feature) => ({
        fillColor: getColor(feature?.properties?.caseCount || 0),
        weight: 2,
        opacity: 1,
        color: "white",
        dashArray: "3",
        fillOpacity: 0.7,
      }),
      onEachFeature: (feature, layer) => {
        const { name, caseCount } = feature.properties;
        layer.bindPopup(`<b>${name}</b><br/>Cases: ${caseCount}`);
      },
    }).addTo(map);
  }, [municipalityData]);

  return (
    <div className="flex flex-col w-full h-screen justify-center items-center m-0 p-0 text-black">
      <h1 className="text-2xl font-bold mb-4">Heatmap Section</h1>
      <div id="map" className="w-full h-[500px] max-w-6xl rounded shadow-lg" />
    </div>
  );
}
