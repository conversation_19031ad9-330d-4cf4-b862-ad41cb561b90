"use client";

import { Pie } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Tooltip, Legend, ChartData } from "chart.js";
import { <PERSON>, CardB<PERSON>, CardHeader, Divider, Spinner } from "@heroui/react";
import { db } from "@/firebase";
import { collection, getDocs } from "firebase/firestore";
import { useEffect, useState } from "react";
import { getAuth } from "firebase/auth";

// Register Chart.js elements
ChartJS.register(ArcElement, Tooltip, Legend);

export default function LacionChart() {
  const [chartData, setChartData] = useState<ChartData<'pie'> | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchChartData = async () => {
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) {
        console.log("No user authenticated for Consolacion chart");
        setLoading(false);
        return;
      }

      try {
        const casesRef = collection(
          db,
          "healthradarDB",
          "users",
          "healthworker",
          user.uid,
          "UploadedCases"
        );

        const snapshot = await getDocs(casesRef);
        const data = snapshot.docs.map((doc) => doc.data());

        console.log("Raw data for Consolacion:", data);

        // 🧠 Filter for Municipality = "Consolacion"
        const filteredData = data.filter((item) => {
          const municipality = item.Municipality?.toLowerCase().trim();
          return municipality === "consolacion";
        });

        console.log("Filtered Consolacion data:", filteredData);

        // 📊 Aggregate case counts by disease within "Consolacion"
        const diseaseMap: Record<string, number> = {};
        filteredData.forEach((item) => {
          const name = item.DiseaseName?.trim();
          const count = parseInt(item.CaseCount, 10);
          if (name && !isNaN(count) && count > 0) {
            diseaseMap[name] = (diseaseMap[name] || 0) + count;
          }
        });

        console.log("Disease map for Consolacion:", diseaseMap);

        const labels = Object.keys(diseaseMap);
        const values = Object.values(diseaseMap);

        if (labels.length > 0) {
          const colors = labels.map(() => `hsl(${Math.random() * 360}, 70%, 60%)`);

          setChartData({
            labels,
            datasets: [
              {
                label: "Disease Case Count",
                data: values,
                backgroundColor: colors,
                hoverOffset: 4,
              },
            ],
          });
        } else {
          console.log("No data found for Consolacion municipality");
          setChartData(null);
        }
      } catch (error) {
        console.error("Error fetching Consolacion chart data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchChartData();
  }, []);

  return (
    <div>
      <Card className="max-w-[400px]">
        <CardHeader className="flex gap-3 items-center text-black">
          Consolacion Chart
        </CardHeader>
        <Divider />
        <CardBody>
          {loading ? (
            <Spinner size="lg" />
          ) : chartData ? (
            <Pie data={chartData} />
          ) : (
            <div className="text-center text-gray-500 p-4">
              No data available for Consolacion
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
}
