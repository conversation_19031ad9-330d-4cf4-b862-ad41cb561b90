"use client";

import { But<PERSON> } from "@heroui/react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { Spinner } from "@heroui/react";
import Cookies from "js-cookie";

export default function SideNavbar() {
  const [loading, setLoading] = useState(false);
  const [routerloading, setRouterLoading] = useState(false);
  const router = useRouter();

  const handleLogout = () => {
    setLoading(true);
    Cookies.remove("token"); // Clear the token from cookies
    // Clear the token from local storage
    // localStorage.removeItem("token");
    // sessionStorage.clear(); // clears all session storage

    console.log("Logout successful");
    // Optionally, redirect to the login page or home page
    setTimeout(() => {
      router.push("/components/login");
    }, 1000);
  };

  const openDiseaseManagement = () => {
    setRouterLoading(true);
    router.push("/sections/diseaseManagement");
    setTimeout(() => {
      setRouterLoading(false);
    }, 1000);
  };

  return (
    <div className="flex flex-col bg-[#A0C878] w-1/5 border border-black  p-4">
      <h1 className="text-2xl font-bold text-black">Side Navbar</h1>
      <ul className="mt-4">
        <li className="mb-2">
          <Button
            className="flex w-full text-white bg-[#EB5B00]"
            // href="/sections/dashboard"
            // className="text-black hover:text-[#EB5B00]"
            onPress={() => router.push("/sections/dashboard")}
          >
            Dashboard
          </Button>
        </li>
        <li className="mb-2">
          <Button
            disabled={loading}
            className="text-white flex w-full bg-[#EB5B00]"
            onPress={openDiseaseManagement}
          >
            {routerloading ? <Spinner size="md" /> : "Disease Management"}
          </Button>
        </li>

        <li className="mb-2">
          <Button
            disabled={loading}
            className="text-white flex w-full bg-[#EB5B00]"
            // onPress={openDiseaseManagement}
            onPress={() => router.push("/sections/heatmap")}
          >
            {/* {routerloading ? <Spinner size="md" /> : "Heat Map"} */}
            Heat Map
          </Button>
        </li>

        <Button
          className="flex w-full text-white bg-[#EB5B00]"
          onPress={handleLogout}
          disabled={loading}
        >
          {loading ? <Spinner size="md" /> : "logout"}
        </Button>
      </ul>
    </div>
  );
}
