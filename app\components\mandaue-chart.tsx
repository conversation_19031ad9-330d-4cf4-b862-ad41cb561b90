"use client";
import { Pie } from "react-chartjs-2";
import { Chart as ChartJ<PERSON>, ArcElement, <PERSON><PERSON><PERSON>, Legend } from "chart.js";
import { <PERSON>, Card<PERSON><PERSON>, CardFooter, CardHeader, Divider } from "@heroui/react";

// Register the required Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

export default function Mandaue<PERSON>hart() {
  const data = {
    labels: ["Red", "Blue", "Yellow"],
    datasets: [
      {
        label: "My First Dataset",
        data: [300, 50, 100],
        backgroundColor: [
          "rgb(255, 99, 132)",
          "rgb(54, 162, 235)",
          "rgb(255, 205, 86)",
        ],
        hoverOffset: 4,
      },
    ],
  };

  return (
    <div>
      {/* <Card className="w-80 h-80 "> */}
      <Card className="max-w-[400px]">
        <CardHeader className="flex gap-3 items-center text-black">
          Mandaue Chart
        </CardHeader>
        <Divider />
        <CardBody>
          <Pie data={data} />
          {/* <Pie className="w-full h-full" data={data} />{" "} */}
        </CardBody>
        {/* <Divider />
        <CardFooter>Visit source code on GitHub.</CardFooter> */}
      </Card>
    </div>
  );
}
