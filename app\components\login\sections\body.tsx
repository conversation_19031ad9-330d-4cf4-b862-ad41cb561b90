"use client";

import { Button, Input } from "@heroui/react";
import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Spinner } from "@heroui/react";
import { signInWithEmailAndPassword } from "firebase/auth";
import { auth } from "../../../../firebase";
import Cookies from "js-cookie";

export default function Login() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  // const handleLogin = async () => {
  //   try {
  //     const response = await axios.post("http://127.0.0.1:8000/api/login", {
  //       email,
  //       password,
  //     });

  //     sessionStorage.setItem("token", response.data.token);
  //     sessionStorage.setItem("email", email);

  //     if (response.status === 200) {
  //       setLoading(true);
  //       // localStorage.setItem("token", response.data.token);
  //       Cookies.set("token", response.data.token);

  //       router.push("/sections/dashboard");

  //       console.log("Login successful");
  //     }
  //   } catch (error) {
  //     // Handle error, e.g., show an error message
  //     setLoading(false);
  //     alert("Login failed. Please check your credentials.");
  //     console.error("Login failed", error);
  //   }
  // };

  //   const logoutToken = sessionStorage.getItem("token")
  //   console.log("Logout Token:", logoutToken);

  const handleLogin = async () => {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
      );
      setLoading(true);
      const token = await userCredential.user.getIdToken();
      // Store token locally
      Cookies.set("token", token);

      console.log("User logged in:", userCredential.user);
      router.push("/sections/dashboard");
    } catch (error) {
      alert("Login failed");
      console.error(error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#DDEB9D] to-[#A0C878] flex items-center justify-center p-6">
      <div className="w-full max-w-lg">
        {/* Header Section */}
        <div className="text-center mb-10">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-white rounded-full mb-6 shadow-lg border-4 border-[#A0C878]">
            <svg className="w-10 h-10 text-[#143D60]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h1 className="text-4xl font-bold text-[#143D60] mb-3">HealthRadar</h1>
          <p className="text-[#143D60] text-lg font-medium">Disease Management System</p>
        </div>

        {/* Login Card */}
        <div className="bg-white rounded-3xl shadow-2xl p-10 border border-white/20 backdrop-blur-sm">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-[#143D60] mb-2">Welcome Back</h2>
            <p className="text-gray-600">Please sign in to your account</p>
          </div>

          <form className="space-y-6" onSubmit={(e) => { e.preventDefault(); handleLogin(); }}>
            {/* Email Input */}
            <div className="space-y-3">
              <label htmlFor="email" className="text-sm font-semibold text-[#143D60] block uppercase tracking-wide">
                Email Address
              </label>
              <Input
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                type="email"
                placeholder="Enter your email address"
                className="w-full"
                classNames={{
                  input: "text-[#143D60] placeholder:text-gray-400 font-medium",
                  inputWrapper: "border-2 border-gray-200 hover:border-[#A0C878] focus-within:border-[#A0C878] bg-gray-50 hover:bg-white transition-all duration-300 h-14"
                }}
                size="lg"
                radius="lg"
                required
              />
            </div>

            {/* Password Input */}
            <div className="space-y-3">
              <label htmlFor="password" className="text-sm font-semibold text-[#143D60] block uppercase tracking-wide">
                Password
              </label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                className="w-full"
                classNames={{
                  input: "text-[#143D60] placeholder:text-gray-400 font-medium",
                  inputWrapper: "border-2 border-gray-200 hover:border-[#A0C878] focus-within:border-[#A0C878] bg-gray-50 hover:bg-white transition-all duration-300 h-14"
                }}
                size="lg"
                radius="lg"
                required
              />
            </div>

            {/* Forgot Password Link */}
            <div className="flex justify-end">
              <Link href="#" className="text-sm text-[#EB5B00] hover:text-[#143D60] font-semibold transition-colors duration-200">
                Forgot Password?
              </Link>
            </div>

            {/* Login Button */}
            <Button
              type="submit"
              className="w-full bg-gradient-to-r from-[#143D60] to-[#1e4a6b] hover:from-[#1e4a6b] hover:to-[#143D60] text-white font-bold py-4 text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              onPress={handleLogin}
              disabled={loading || !email || !password}
              size="lg"
              radius="lg"
            >
              {loading ? (
                <div className="flex items-center gap-3">
                  <Spinner size="sm" color="white" />
                  <span>Signing In...</span>
                </div>
              ) : (
                "Sign In"
              )}
            </Button>

            {/* Divider */}
            <div className="relative my-8">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t-2 border-gray-200"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-6 bg-white text-gray-500 font-medium">New to HealthRadar?</span>
              </div>
            </div>

            {/* Sign Up Button */}
            <Button
              as={Link}
              href="/components/signup"
              className="w-full bg-gradient-to-r from-[#EB5B00] to-[#ff6b1a] hover:from-[#ff6b1a] hover:to-[#EB5B00] text-white font-bold py-4 text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              size="lg"
              radius="lg"
            >
              Create New Account
            </Button>
          </form>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-[#143D60] font-medium">© 2024 HealthRadar. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
}
