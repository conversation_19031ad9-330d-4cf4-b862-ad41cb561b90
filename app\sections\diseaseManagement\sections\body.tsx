"use client";

import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Input,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
} from "@heroui/react";
import Papa from "papaparse";
import { getDocs, query, deleteDoc, doc } from "firebase/firestore";
import { collection, addDoc } from "firebase/firestore";
import { db } from "@/firebase";

import { useEffect, useState, useCallback } from "react";
import { getAuth } from "firebase/auth";

interface CsvData {
  [key: string]: string | number;
}

export default function Body() {
  const [ArrayKeys, setArrayKeys] = useState<string[]>([]);
  const [ArrayValues, setArrayValues] = useState<CsvData[]>([]);
  const [loading, setLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  const auth = getAuth();
  const user = auth.currentUser;

  // const diseaseManagement = () => {
  //   setTableLoading(true);

  //   if (data.length > 0) {
  //     setTableLoading(false);
  //     return ""; // Always return a string or ReactNode
  //   } else {
  //     setTableLoading(false);
  //     return "No data available. Please upload a CSV file to view the data.";
  //   }
  // };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setLoading(true);
    const file = event.target.files?.[0];
    if (!file) return;

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: async (results) => {
        console.log("Parsed Results:", results.data);
        const parsedData = results.data as CsvData[];

        if (parsedData.length > 0) {
          const keys = Object.keys(parsedData[0]);
          setArrayKeys(keys);
          setArrayValues(parsedData);
        }

        console.log("Current User:", user?.email);

        if (!user) {
          console.error("User not authenticated.");
          return;
        } else {
          console.log("User authenticated:", user);
        }

        try {
          await Promise.all(
            parsedData.map(async (disease) => {
              await addDoc(
                collection(
                  db,
                  "healthradarDB",
                  "users",
                  "healthworker",
                  user.uid,
                  "UploadedCases"
                ),
                disease
              );
              console.log("Disease uploaded:", disease);
            })
          );
        } catch (error) {
          console.error("Error uploading documents to Firestore:", error);
        }
      },
    });
  };

  // 🟢 Fetch uploaded cases from Firebase
  const fetchUploadedCases = useCallback(async () => {
    if (!user) return;

    const casesQuery = collection(
      db,
      "healthradarDB",
      "users",
      "healthworker",
      user.uid,
      "UploadedCases"
    );

    const querySnapshot = await getDocs(query(casesQuery));

    const fetchedData: CsvData[] = [];
    querySnapshot.forEach((doc) => {
      fetchedData.push(doc.data() as CsvData);
    });

    if (fetchedData.length > 0) {
      const allKeys = [
        ...new Set(fetchedData.flatMap((doc) => Object.keys(doc))),
      ];
      setArrayKeys(allKeys);
      setArrayValues(fetchedData);
    }
    // if (fetchedData.length > 0) {
    //   setData(fetchedData);
    //   setArrayKeys(Object.keys(fetchedData[0]));
    //   setArrayValues(fetchedData.map(Object.values));
    //   // setArrayValues(fetchedData.map(Object.values));
    //   setTableLoading(true);
    // }
  }, [user]);

  // �️ Delete all uploaded cases
  const deleteAllCases = useCallback(async () => {
    if (!user) {
      console.log("No user authenticated");
      return;
    }

    setDeleteLoading(true);
    try {
      const casesRef = collection(
        db,
        "healthradarDB",
        "users",
        "healthworker",
        user.uid,
        "UploadedCases"
      );

      const querySnapshot = await getDocs(query(casesRef));

      // Delete all documents
      const deletePromises = querySnapshot.docs.map((document) =>
        deleteDoc(doc(db, "healthradarDB", "users", "healthworker", user.uid, "UploadedCases", document.id))
      );

      await Promise.all(deletePromises);

      console.log(`Deleted ${querySnapshot.docs.length} records`);

      // Clear the local state
      setArrayKeys([]);
      setArrayValues([]);

      alert(`Successfully deleted ${querySnapshot.docs.length} records`);
    } catch (error) {
      console.error("Error deleting cases:", error);
      alert("Error deleting data. Please try again.");
    } finally {
      setDeleteLoading(false);
    }
  }, [user]);

  // �🔄 Fetch on component mount
  useEffect(() => {
    if (user) {
      fetchUploadedCases();
    }
  }, [user, fetchUploadedCases]);

  return (
    <div className="flex flex-col justify-center h-full gap-4 m-4">
      <div className="flex justify-between">
        <Input
          className="w-52 items-center"
          placeholder="Search Disease name"
        ></Input>
        <div className="flex justify-between gap-4">
          <Button onPress={onOpen} disabled={loading} className="bg-white">
            {/* {loading ? <Spinner size="md" /> : "Upload CSV"} */}
            Upload CSV
          </Button>
          <Button
            onPress={deleteAllCases}
            disabled={deleteLoading || ArrayValues.length === 0}
            className="bg-red-500 text-white hover:bg-red-600"
          >
            {deleteLoading ? "Deleting..." : "Delete All CSV Data"}
          </Button>
          <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
            <ModalContent>
              {(onClose) => (
                <>
                  <ModalHeader className="text-lg font-semibold">
                    Upload CSV
                  </ModalHeader>
                  <ModalBody className="pb-0">
                    <div className="flex items-center m-4 w-full">
                      <Input
                        type="file"
                        accept=".csv"
                        onChange={(e) => {
                          handleFileUpload(e);
                          onClose();
                        }}
                        description="Please upload a CSV file."
                        className="w-full"
                      />
                    </div>
                  </ModalBody>
                  <ModalFooter className="mt-0">
                    <Button color="danger" variant="light" onPress={onClose}>
                      Close
                    </Button>
                  </ModalFooter>
                </>
              )}
            </ModalContent>
          </Modal>

          {/* <Dropdown>
            <DropdownTrigger>
              <Button variant="bordered">Open Menu</Button>
            </DropdownTrigger>
            <DropdownMenu aria-label="Static Actions">
              <DropdownItem key="new">New file</DropdownItem>
              <DropdownItem key="copy">Copy link</DropdownItem>
              <DropdownItem key="edit">Edit file</DropdownItem>
              <DropdownItem key="delete" className="text-danger" color="danger">
                Delete file
              </DropdownItem>
            </DropdownMenu>
          </Dropdown> */}
        </div>
      </div>
      <div>
        <Table
          aria-label="Example static collection table"
          className="text-black"
        >
          <TableHeader>
            {ArrayKeys.map((key: string) => (
              <TableColumn key={key}>{key}</TableColumn>
            ))}
          </TableHeader>
          <TableBody>
            {ArrayValues.map((row: CsvData, index: number) => (
              <TableRow key={index}>
                {ArrayKeys.map((key: string, idx: number) => (
                  <TableCell key={idx}>{row[key] || ""}</TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
