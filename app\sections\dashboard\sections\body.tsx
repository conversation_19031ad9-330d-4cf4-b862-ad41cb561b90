"use client";

import Mandaue<PERSON>hart from "../../../components/mandaue-chart";
import Lacion<PERSON>hart from "../../../components/lacion-chart";
import Lil<PERSON><PERSON><PERSON> from "../../../components/liloan-chart";
import NewsBox from "../../../components/news-box";

import Prediction<PERSON><PERSON> from "../../../components/prediction-chart";

export default function Body() {
  // const router = useRouter();
  // useEffect(() => {
  //   const token = sessionStorage.getItem("token");

  //   if (!token) {
  //     //router.push("http://localhost:3000");
  //     router.push("/sections/login");
  //     console.log("No token found, redirecting to login page.");
  //   } else {
  //     console.log("Token found:", token);
  //   }
  // });
  return (
    <div className="flex flex-col  bg-[#DDEB9D] p-4 gap-4">
      <div className="flex gap-4 ">
        <PredictionChart />
        <NewsBox />
      </div>
      <div className="flex gap-4 bg-[#DDEB9D] w-full justify-evenly">
        <MandaueChart />
        <LacionChart />
        <LiloanChart />
      </div>
    </div>
  );
}
