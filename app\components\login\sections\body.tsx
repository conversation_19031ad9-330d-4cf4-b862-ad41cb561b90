"use client";

import { Button, Input } from "@heroui/react";
import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Spinner } from "@heroui/react";
import { signInWithEmailAndPassword } from "firebase/auth";
import { auth } from "../../../../firebase";
import Cookies from "js-cookie";

export default function Login() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  // const handleLogin = async () => {
  //   try {
  //     const response = await axios.post("http://127.0.0.1:8000/api/login", {
  //       email,
  //       password,
  //     });

  //     sessionStorage.setItem("token", response.data.token);
  //     sessionStorage.setItem("email", email);

  //     if (response.status === 200) {
  //       setLoading(true);
  //       // localStorage.setItem("token", response.data.token);
  //       Cookies.set("token", response.data.token);

  //       router.push("/sections/dashboard");

  //       console.log("Login successful");
  //     }
  //   } catch (error) {
  //     // Handle error, e.g., show an error message
  //     setLoading(false);
  //     alert("Login failed. Please check your credentials.");
  //     console.error("Login failed", error);
  //   }
  // };

  //   const logoutToken = sessionStorage.getItem("token")
  //   console.log("Logout Token:", logoutToken);

  const handleLogin = async () => {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
      );
      setLoading(true);
      const token = await userCredential.user.getIdToken();
      // Store token locally
      Cookies.set("token", token);

      console.log("User logged in:", userCredential.user);
      router.push("/sections/dashboard");
    } catch (error) {
      alert("Login failed");
      console.error(error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo/Brand Section */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full mb-4 shadow-lg">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">HealthRadar</h1>
          <p className="text-gray-600">Sign in to your account</p>
        </div>

        {/* Login Form */}
        <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
          <form className="space-y-6" onSubmit={(e) => { e.preventDefault(); handleLogin(); }}>
            {/* Email Input */}
            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium text-gray-700 block">
                Email Address
              </label>
              <Input
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                type="email"
                placeholder="Enter your email"
                className="w-full"
                classNames={{
                  input: "text-gray-900 placeholder:text-gray-400",
                  inputWrapper: "border-gray-200 hover:border-gray-300 focus-within:border-blue-500 bg-gray-50 hover:bg-white transition-colors"
                }}
                size="lg"
                radius="lg"
                required
              />
            </div>

            {/* Password Input */}
            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium text-gray-700 block">
                Password
              </label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                className="w-full"
                classNames={{
                  input: "text-gray-900 placeholder:text-gray-400",
                  inputWrapper: "border-gray-200 hover:border-gray-300 focus-within:border-blue-500 bg-gray-50 hover:bg-white transition-colors"
                }}
                size="lg"
                radius="lg"
                required
              />
            </div>

            {/* Forgot Password Link */}
            <div className="flex justify-end">
              <Link href="#" className="text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors">
                Forgot your password?
              </Link>
            </div>

            {/* Login Button */}
            <Button
              type="submit"
              className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 transition-all duration-200 shadow-lg hover:shadow-xl"
              onPress={handleLogin}
              disabled={loading || !email || !password}
              size="lg"
              radius="lg"
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <Spinner size="sm" color="white" />
                  <span>Signing in...</span>
                </div>
              ) : (
                "Sign In"
              )}
            </Button>

            {/* Divider */}
            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white text-gray-500">Don&apos;t have an account?</span>
              </div>
            </div>

            {/* Sign Up Button */}
            <Button
              as={Link}
              href="/components/signup"
              className="w-full bg-white border-2 border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 font-semibold py-3 transition-all duration-200 hover:bg-gray-50"
              size="lg"
              radius="lg"
            >
              Create New Account
            </Button>
          </form>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>© 2024 HealthRadar. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
}
