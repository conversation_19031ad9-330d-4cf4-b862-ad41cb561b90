"use client";

import { Button, Input } from "@heroui/react";
import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Spinner } from "@heroui/react";
import { signInWithEmailAndPassword } from "firebase/auth";
import { auth } from "../../../../firebase";
import Cookies from "js-cookie";

export default function Login() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  // const handleLogin = async () => {
  //   try {
  //     const response = await axios.post("http://127.0.0.1:8000/api/login", {
  //       email,
  //       password,
  //     });

  //     sessionStorage.setItem("token", response.data.token);
  //     sessionStorage.setItem("email", email);

  //     if (response.status === 200) {
  //       setLoading(true);
  //       // localStorage.setItem("token", response.data.token);
  //       Cookies.set("token", response.data.token);

  //       router.push("/sections/dashboard");

  //       console.log("Login successful");
  //     }
  //   } catch (error) {
  //     // Handle error, e.g., show an error message
  //     setLoading(false);
  //     alert("Login failed. Please check your credentials.");
  //     console.error("Login failed", error);
  //   }
  // };

  //   const logoutToken = sessionStorage.getItem("token")
  //   console.log("Logout Token:", logoutToken);

  const handleLogin = async () => {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
      );
      setLoading(true);
      const token = await userCredential.user.getIdToken();
      // Store token locally
      Cookies.set("token", token);

      console.log("User logged in:", userCredential.user);
      router.push("/sections/dashboard");
    } catch (error) {
      alert("Login failed");
      console.error(error);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center h-screen bg-[#DDEB9D]">
      <h1 className="text-4xl font-bold text-black">Login</h1>
      <form className="flex flex-col mt-4 w-1/3 ">
        <Input
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          type="text"
          placeholder="Username"
          className="mb-2 p-2  text-black"
        />
        <Input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder="Password"
          className="mb-2 p-2  text-black"
        />
        <div className="flex justify-evenly gap-4 w-full">
          <Button
            className="flex w-full text-white bg-[#143D60]"
            onPress={handleLogin}
            disabled={loading}
          >
            {loading ? <Spinner size="md" /> : "Login"}
          </Button>
          <Button
            className="flex w-full text-white bg-[#EB5B00]"
            as={Link}
            href="/components/signup"
          >
            Sign up
          </Button>
        </div>
      </form>
    </div>
  );
}
