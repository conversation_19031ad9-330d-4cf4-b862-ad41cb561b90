"use client";

import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { <PERSON>, CardBody, CardHeader, Divider } from "@heroui/react";

// Register the necessary Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

export default function PredictionChart() {
  // Generate the labels (months)
  const labels = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
  ];

  // Define the data for the chart
  const data = {
    labels: labels,
    datasets: [
      {
        label: "My First Dataset",
        data: [100, 59, 80, 81, 56, 55, 40],
        fill: false, // No fill under the line
        borderColor: "rgb(75, 192, 192)", // Line color
        tension: 0.1, // Smoothness of the line
      },
    ],
  };

  // Options for chart customization
  const options = {
    responsive: true, // Makes the chart responsive to window resizing
    maintainAspectRatio: false, // Allow chart to resize without maintaining aspect ratio
    aspectRatio: 2, // Custom aspect ratio (width / height), you can adjust this value
  };

  return (
    <div className="w-full ">
      <Card className="w-full h-80">
        {" "}
        {/* Set height of the Card */}
        <CardHeader className="flex gap-3 text-center text-black">
          Predictive Analytics
        </CardHeader>
        <Divider />
        <CardBody className="p-0">
          {" "}
          {/* Remove padding to make the chart fill the container */}
          <div className="w-full h-full">
            <Line data={data} options={options} />
          </div>
        </CardBody>
        {/* <Divider />
        <CardFooter></CardFooter> */}
      </Card>
    </div>
  );
}
