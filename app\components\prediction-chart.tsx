"use client";

import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";


// Register the necessary Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

export default function PredictionChart() {
  // Generate the labels (months)
  const labels = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
  ];

  // Define the data for the chart
  const data = {
    labels: labels,
    datasets: [
      {
        label: "My First Dataset",
        data: [100, 59, 80, 81, 56, 55, 40],
        fill: false, // No fill under the line
        borderColor: "rgb(75, 192, 192)", // Line color
        tension: 0.1, // Smoothness of the line
      },
    ],
  };

  // Options for chart customization
  const options = {
    responsive: true, // Makes the chart responsive to window resizing
    maintainAspectRatio: false, // Allow chart to resize without maintaining aspect ratio
    aspectRatio: 2, // Custom aspect ratio (width / height), you can adjust this value
  };

  return (
    <div className="w-full h-full">
      <div className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-gradient-to-r from-[#143D60] to-[#1e4a6b] rounded-lg flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <div>
            <h3 className="text-xl font-bold text-[#143D60]">Predictive Analytics</h3>
            <p className="text-sm text-gray-600">Disease trend forecasting</p>
          </div>
        </div>
        <div className="h-80 bg-gray-50 rounded-xl p-4">
          <Line data={data} options={options} />
        </div>
      </div>
    </div>
  );
}
