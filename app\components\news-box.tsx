"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "@heroui/react";
import { useState, useEffect } from "react";

const ApiUrl = `https://newsapi.org/v2/top-headlines?country=us&category=business&apiKey=cfa20054a3fe4077a4f572c61fa2a8c4`;

interface NewsArticle {
  title: string;
  description: string;
  url: string;
}

export default function NewsBox() {
  const [news, setNews] = useState<NewsArticle[]>([]); // Use an array to hold multiple articles

  useEffect(() => {
    const fetchData = async () => {
      const result = await fetch(ApiUrl);
      result.json().then((json) => {
        setNews(json.articles); // Store the articles directly in the state
        console.log(json);
      });
    };
    fetchData();
  }, []); // Make sure this effect runs only once on mount

  return (
    <div>
      <Card className="w-96 max-h-80">
        <CardHeader className="flex gap-3">
          <div className="flex flex-col">
            <p className="text-md text-black items-center">Related News</p>
          </div>
        </CardHeader>
        <Divider />
        <CardBody>
          {/* Loop through the news articles and display each title */}
          {news.length > 0 ? (
            news.map((article, index) => (
              <div key={index} className="mb-4">
                <h3 className="text-lg font-semibold">{article.title}</h3>
                <p className="text-sm text-gray-600">{article.description}</p>
                <Link href={article.url} isExternal>
                  Read more
                </Link>
              </div>
            ))
          ) : (
            <p>Loading news...</p>
          )}
        </CardBody>
        <Divider />
        <CardFooter>
          <Link isExternal showAnchorIcon href="https://newsapi.org/">
            Powered by NewsAPI
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
